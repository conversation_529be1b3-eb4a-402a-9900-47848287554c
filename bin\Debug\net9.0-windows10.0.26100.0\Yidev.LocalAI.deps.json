{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Yidev.LocalAI/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": "9.0.6", "Microsoft.Extensions.Hosting": "9.0.6", "Microsoft.SemanticKernel": "1.57.0", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.57.0", "Microsoft.SemanticKernel.Plugins.Memory": "1.57.0-alpha", "ModelContextProtocol-SemanticKernel": "0.3.0-preview-01", "Serilog": "4.3.0", "Serilog.Extensions.Hosting": "9.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "7.0.0", "Yidev.Agent.Oidc": "1.0.1", "Microsoft.Web.WebView2.Core": "1.0.3296.44", "Microsoft.Web.WebView2.WinForms": "1.0.3296.44", "Microsoft.Web.WebView2.Wpf": "1.0.3296.44", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.26100.54"}, "runtime": {"Yidev.LocalAI.dll": {}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.26100.54": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.26100.38", "fileVersion": "10.0.26100.52"}, "WinRT.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.6.47881"}}}, "Azure.AI.OpenAI/2.2.0-beta.4": {"dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.2.0-beta.4", "System.ClientModel": "1.4.0-beta.1"}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "2.200.25.16901"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "9.0.5", "System.Memory.Data": "9.0.5", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.6", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "IdentityModel/5.2.0": {"runtime": {"lib/net5.0/IdentityModel.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.0"}}}, "IdentityModel.OidcClient/5.2.1": {"dependencies": {"IdentityModel": "5.2.0", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/netstandard2.0/IdentityModel.OidcClient.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.2.1.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Data.Sqlite.Core/9.0.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.6"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.Extensions.AI/9.5.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.5", "System.Text.Json": "9.0.6", "System.Threading.Channels": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "9.5.0.0", "fileVersion": "9.500.25.26507"}}}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.6.0.0", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "OpenAI": "2.2.0-beta.4", "System.Memory.Data": "9.0.5", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AI.OpenAI.dll": {"assemblyVersion": "9.5.0.0", "fileVersion": "9.500.25.26507"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.CommandLine": "9.0.6", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Logging.Console": "9.0.6", "Microsoft.Extensions.Logging.Debug": "9.0.6", "Microsoft.Extensions.Logging.EventLog": "9.0.6", "Microsoft.Extensions.Logging.EventSource": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.EventLog": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.DataAnnotations/3.1.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "3.1.7.0", "fileVersion": "3.100.720.37108"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.6.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.SemanticKernel/1.57.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.57.0", "Microsoft.SemanticKernel.Core": "1.57.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.57.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.5.0", "Microsoft.Extensions.VectorData.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.57.0": {"dependencies": {"Azure.AI.OpenAI": "2.2.0-beta.4", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.57.0", "Microsoft.SemanticKernel.Core": "1.57.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.57.0": {"dependencies": {"Microsoft.Extensions.AI.OpenAI": "9.5.0-preview.1.25265.7", "Microsoft.SemanticKernel.Core": "1.57.0", "OpenAI": "2.2.0-beta.4"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.SemanticKernel.Core/1.57.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.SemanticKernel.Abstractions": "1.57.0", "System.Numerics.Tensors": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.SemanticKernel.Plugins.Memory/1.57.0-alpha": {"dependencies": {"Microsoft.SemanticKernel.Core": "1.57.0", "System.Numerics.Tensors": "9.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.Memory.dll": {"assemblyVersion": "1.57.0.0", "fileVersion": "1.57.0.0"}}}, "Microsoft.Web.WebView2/1.0.3296.44": {"runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.3296.44"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.3296.44"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.3296.44"}}}, "ModelContextProtocol/0.3.0-preview.1": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "ModelContextProtocol.Core": "0.3.0-preview.1"}, "runtime": {"lib/net9.0/ModelContextProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ModelContextProtocol-SemanticKernel/0.3.0-preview-01": {"dependencies": {"Microsoft.SemanticKernel": "1.57.0", "ModelContextProtocol": "0.3.0-preview.1", "Stef.Validation.Options": "0.2.0"}, "runtime": {"lib/net8.0/ModelContextProtocol-SemanticKernel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ModelContextProtocol.Core/0.3.0-preview.1": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Net.ServerSentEvents": "10.0.0-preview.4.25258.110"}, "runtime": {"lib/net9.0/ModelContextProtocol.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenAI/2.2.0-beta.4": {"dependencies": {"System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "9.0.5"}, "runtime": {"lib/net8.0/OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/4.3.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Serilog": "4.3.0", "Serilog.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.6", "Serilog": "4.3.0"}, "runtime": {"lib/net9.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net9.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "Stef.Validation/0.2.0": {"runtime": {"lib/net6.0/Stef.Validation.dll": {"assemblyVersion": "0.2.0.0", "fileVersion": "0.2.0.0"}}}, "Stef.Validation.Options/0.2.0": {"dependencies": {"Microsoft.Extensions.Options.DataAnnotations": "3.1.7", "Stef.Validation": "0.2.0"}, "runtime": {"lib/netstandard2.1/Stef.Validation.Options.dll": {"assemblyVersion": "0.2.0.0", "fileVersion": "0.2.0.0"}}}, "System.ClientModel/1.4.0-beta.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Memory.Data": "9.0.5"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.400.25.15605"}}}, "System.Diagnostics.DiagnosticSource/9.0.5": {"runtime": {"lib/net9.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Diagnostics.EventLog/9.0.6": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Memory/4.5.3": {}, "System.Memory.Data/9.0.5": {"runtime": {"lib/net9.0/System.Memory.Data.dll": {"assemblyVersion": "9.0.0.5", "fileVersion": "9.0.525.21509"}}}, "System.Net.ServerSentEvents/10.0.0-preview.4.25258.110": {"runtime": {"lib/net9.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.25910"}}}, "System.Numerics.Tensors/9.0.0": {"runtime": {"lib/net9.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/9.0.6": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.Channels/9.0.5": {"runtime": {"lib/net9.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Yidev.Agent.Oidc/1.0.1": {"dependencies": {"IdentityModel.OidcClient": "5.2.1", "Microsoft.Web.WebView2": "1.0.3296.44"}, "runtime": {"lib/net9.0-windows10.0.19041.0/Yidev.Agent.Oidc.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Web.WebView2.Core/1.0.3296.44": {"runtime": {"Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.3296.44", "fileVersion": "1.0.3296.44"}}}, "Microsoft.Web.WebView2.WinForms/1.0.3296.44": {"runtime": {"Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.3296.44", "fileVersion": "1.0.3296.44"}}}, "Microsoft.Web.WebView2.Wpf/1.0.3296.44": {"runtime": {"Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.3296.44", "fileVersion": "1.0.3296.44"}}}}}, "libraries": {"Yidev.LocalAI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.26100.54": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-qjCgspdq67x+urifvf7Dkz4tX5HVU3AlF2XUYU/kQBObKQihPsTYSQJ4tiMHEMNjaKRbfHzxnE2vnuhcqUUWCg==", "path": "azure.ai.openai/2.2.0-beta.4", "hashPath": "azure.ai.openai.2.2.0-beta.4.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "IdentityModel/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nuhkbaDH9l5QzNJp2MtP3qio57MPtiRneUN8Ocr7od0JvSYaIe3gBj/vxllr11S/Qvu1AG4GZXoyv5469ewYDA==", "path": "identitymodel/5.2.0", "hashPath": "identitymodel.5.2.0.nupkg.sha512"}, "IdentityModel.OidcClient/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-OuPhDNAw/EoJVEmYO6/ChZUBcug4OGoGKTKLUyBCsGhlKegxJk25LYQ0EL7GCBMgkEL+BYNJukNZyaJ+JNaWog==", "path": "identitymodel.oidcclient/5.2.1", "hashPath": "identitymodel.oidcclient.5.2.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "path": "microsoft.data.sqlite.core/9.0.6", "hashPath": "microsoft.data.sqlite.core.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bVSdfFrqIo3ZeQfWYYfnVVanP1GWghkdw+MnEmZJz7jUwtdPQpBKHr0BW9dMizPamzU+SMA1Qu4nXuRTlKVAGQ==", "path": "microsoft.entityframeworkcore.sqlite/9.0.6", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-xP+SvMDR/GZCDNXFw7z4WYbO2sYpECvht3+lqejg+Md8vLtURwTBvdsOUAnY4jBGmNFqHeh87hZSmUGmuxyqMA==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.6", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.AI/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-RBq/TspXI7UScO5+fHXIrnlDvX8pnvFcHWag3OPPivJgjqmI/RF70dpTPmMIly9dG+z+86kLP2KGkA2UiZ9tVQ==", "path": "microsoft.extensions.ai/9.5.0", "hashPath": "microsoft.extensions.ai.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-xGO7rHg3qK8jRdriAxIrsH4voNemCf8GVmgdcPXI5gpZ6lZWqOEM4ZO8yfYxUmg7+URw2AY1h7Uc/H17g7X1Kw==", "path": "microsoft.extensions.ai.abstractions/9.6.0", "hashPath": "microsoft.extensions.ai.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"type": "package", "serviceable": true, "sha512": "sha512-htxD19JfZekY2vJSoMJn6lkBlZLcgMm7iK0MZc8pmuVT7FfNP7o+mTS4S0ZUDdBm1YR7NzZfYYjbwFOK/Z1gKg==", "path": "microsoft.extensions.ai.openai/9.5.0-preview.1.25265.7", "hashPath": "microsoft.extensions.ai.openai.9.5.0-preview.1.25265.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "path": "microsoft.extensions.configuration/9.0.6", "hashPath": "microsoft.extensions.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "path": "microsoft.extensions.configuration.binder/9.0.6", "hashPath": "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-DC5I4Y1nK35jY4piDqQCzWjDXzT6ECMctBAxgAJoc6pn0k6uyxcDeOuVDRooFui/N65ptn9xT5mk9eO4mSTj/g==", "path": "microsoft.extensions.configuration.commandline/9.0.6", "hashPath": "microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-RGYG2JBak9lf2rIPiZUVmWjUqoxaHPy3XPhPsJyIQ8QqK47rKvJz7jxVYefTnYdM5LTEiGFBdC7v3+SiosvmkQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.6", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "path": "microsoft.extensions.configuration.json/9.0.6", "hashPath": "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0ZZMzdvNwIS0f09S0IcaEbKFm+Xc41vRROsA/soeKEpzRISTDdiVwGlzdldbXEsuPjNVvNHyvIP8YW2hfIig0w==", "path": "microsoft.extensions.configuration.usersecrets/9.0.6", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "path": "microsoft.extensions.dependencymodel/9.0.6", "hashPath": "microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "path": "microsoft.extensions.diagnostics/9.0.6", "hashPath": "microsoft.extensions.diagnostics.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-1HJ<PERSON>bwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Iu1UyXUnjMhoOwThKM0kCyjgWqqQnuujsbPMnF44ITUbmETT7RAVlozNgev2L/damwNoPZKpmwArRKBy2IOAZg==", "path": "microsoft.extensions.hosting/9.0.6", "hashPath": "microsoft.extensions.hosting.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "path": "microsoft.extensions.hosting.abstractions/9.0.6", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "path": "microsoft.extensions.logging.configuration/9.0.6", "hashPath": "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "path": "microsoft.extensions.logging.console/9.0.6", "hashPath": "microsoft.extensions.logging.console.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-u21euQdOjaEwmlnnB1Zd4XGqOmWI8FkoGeUleV7n4BZ8HPQC/jrYzX/B5Cz3uI/FXjd//W88clPfkGIbSif7Jw==", "path": "microsoft.extensions.logging.debug/9.0.6", "hashPath": "microsoft.extensions.logging.debug.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IyyGy7xNJAjdlFYXc7SZ7kS3CWd3Ma4hing9QGtzXi+LXm8RWCEXdKA1cPx5AeFmdg3rVG+ADGIn44K14O+vFA==", "path": "microsoft.extensions.logging.eventlog/9.0.6", "hashPath": "microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-ayCRr/8ON3aINH81ak9l3vLAF/0pV/xrfChCbIlT2YnHAd4TYBWLcWhzbJWwPFV4XmJFrx/z8oq+gZzIc/74OA==", "path": "microsoft.extensions.logging.eventsource/9.0.6", "hashPath": "microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.DataAnnotations/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-RvBASYloUdwQwLoBMZ7SXu2FMskMELHI6bq+yvfkiggsCkL4gVT20sXG9EzafuIwqApvNmUH0UOulN7bmj1C4Q==", "path": "microsoft.extensions.options.dataannotations/3.1.7", "hashPath": "microsoft.extensions.options.dataannotations.3.1.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-7ZtvjmaTCwkOYZmNU7pQON8v6wfo8HlMkOQeONMhChivSm/etcb0ZPQOnQAkQwR6WeYx4sOHxyiDMii5our5+w==", "path": "microsoft.extensions.vectordata.abstractions/9.6.0", "hashPath": "microsoft.extensions.vectordata.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.SemanticKernel/1.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-FOue7boVeTqcA1Fy1bAnbURHH6CzNpj+03Oi1nKXTscaKF2JE8LUYOpX3nyIXoF3NxV6GWYcv88k9aKjR4ACbA==", "path": "microsoft.semantickernel/1.57.0", "hashPath": "microsoft.semantickernel.1.57.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-Dum6NfNSaqDHx6B1qpb+Hsy3H46u+UForoEAwhZYNeFR1yy6qUKEqhVSKHaLWKvbuIgTpyLTWNmMQRR8lxJ2vg==", "path": "microsoft.semantickernel.abstractions/1.57.0", "hashPath": "microsoft.semantickernel.abstractions.1.57.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-T4EcaNLV4GmUYhWnpEuRGB4m2uLZu3tpaleQZareM9LJ+jYhKftY1qk0fVRumwJtA1vpCWi/qxbmNlw8OoBoWQ==", "path": "microsoft.semantickernel.connectors.azureopenai/1.57.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.57.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-IjjZt6C/LHukzSPGeCKYHO+82osjwWg7TnW5g0SuwyecqOyyUYWKxPUgdrp/jy2f4slkI8zzkQzt9MV/77nXiA==", "path": "microsoft.semantickernel.connectors.openai/1.57.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.57.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-FrCd50rcjY/XVa4+Zqkj8YeYxcr97ujK+o9kGTEaSr7eN93R4xz1BMwxBPuSsgqd1Brwfjyf3AWXfMSQLiPa5A==", "path": "microsoft.semantickernel.core/1.57.0", "hashPath": "microsoft.semantickernel.core.1.57.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Plugins.Memory/1.57.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-76DEXCl6Bq0uTiK8Z2P5cRsWi8LGEtzpoKv/j/nv+BmjlNwwHM4Yz+5Umz77pW7wvok03qTFTeHaHzaeBEkiCg==", "path": "microsoft.semantickernel.plugins.memory/1.57.0-alpha", "hashPath": "microsoft.semantickernel.plugins.memory.1.57.0-alpha.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.3296.44": {"type": "package", "serviceable": true, "sha512": "sha512-dDCbfdpka+X9TDpTqlJY1Ydz+BQ/KOYNtnJvXXqtBONe96rkwk9wZSLasw7yCo4NI1rXuDg2qAfDtV9K1vl8eg==", "path": "microsoft.web.webview2/1.0.3296.44", "hashPath": "microsoft.web.webview2.1.0.3296.44.nupkg.sha512"}, "ModelContextProtocol/0.3.0-preview.1": {"type": "package", "serviceable": true, "sha512": "sha512-il9gKINohZ4V4zpsNELg7fNch89D1GcmsnyMDWMnaH5TURDGPwXdw1gfUAc1D/K2jLGHDWNeIkPyuak+uzZSWA==", "path": "modelcontextprotocol/0.3.0-preview.1", "hashPath": "modelcontextprotocol.0.3.0-preview.1.nupkg.sha512"}, "ModelContextProtocol-SemanticKernel/0.3.0-preview-01": {"type": "package", "serviceable": true, "sha512": "sha512-81BnaP1Dl4iTzHiEsKXEwYxqxX5mluyGv8v/g3JZMb22EvyGFB6o0vZ/lzFydCCDdEiuNPJ/h6dyPxpVhabMvQ==", "path": "modelcontextprotocol-semantickernel/0.3.0-preview-01", "hashPath": "modelcontextprotocol-semantickernel.0.3.0-preview-01.nupkg.sha512"}, "ModelContextProtocol.Core/0.3.0-preview.1": {"type": "package", "serviceable": true, "sha512": "sha512-KAMAog1SioXF2x22yfS6RltIykAs7J95qqidM8oW3cD8cSeYDdOVK1MbbWaTPsvMMcCi5uV4givwGyveubDErw==", "path": "modelcontextprotocol.core/0.3.0-preview.1", "hashPath": "modelcontextprotocol.core.0.3.0-preview.1.nupkg.sha512"}, "OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-JZ4/mlVXLaXDIZuC4Ddu0KCAA23z4Ax1AQTS26mpJRuSShjXik7DU8a3basY3ddD51W04F7jeX5eAXamKA6rHw==", "path": "openai/2.2.0-beta.4", "hashPath": "openai.2.2.0-beta.4.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "path": "serilog.extensions.hosting/9.0.0", "hashPath": "serilog.extensions.hosting.9.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "path": "serilog.extensions.logging/9.0.0", "hashPath": "serilog.extensions.logging.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "Stef.Validation/0.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-X43dtF+Vbqjg1apJ3yzzceznLyW6ziBB2ZvjsB0jtJfpZZOejwXPCS5NNWmwu+NXaD09pJJ3+rCk4Bdd7zWA8w==", "path": "stef.validation/0.2.0", "hashPath": "stef.validation.0.2.0.nupkg.sha512"}, "Stef.Validation.Options/0.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-h9zOsasuH0cX5Vyv0qgIVv+oCdO3dCGV3nEL+T/QWTJezF1uNHfdFilneyzryNjuHAQ5DFeydrYmwpwUioK4JA==", "path": "stef.validation.options/0.2.0", "hashPath": "stef.validation.options.0.2.0.nupkg.sha512"}, "System.ClientModel/1.4.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZR0fKC94VS4P80vmxjk7l13/jPBXV0GMoE4jQfkYk8m2YV+dlw8jSC+b6eAfyBz0u+soN4CjhT3OdOC5KHaXxg==", "path": "system.clientmodel/1.4.0-beta.1", "hashPath": "system.clientmodel.1.4.0-beta.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WoI5or8kY2VxFdDmsaRZ5yaYvvb+4MCyy66eXo79Cy1uMa7qXeGIlYmZx7R9Zy5S4xZjmqvkk2V8L6/vDwAAEA==", "path": "system.diagnostics.diagnosticsource/9.0.5", "hashPath": "system.diagnostics.diagnosticsource.9.0.5.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-lum+Dv+8S4gqN5H1C576UcQe0M2buoRjEUVs4TctXRSWjBH3ay3w2KyQrOo1yPdRs1I+xK69STz+4mjIisFI5w==", "path": "system.diagnostics.eventlog/9.0.6", "hashPath": "system.diagnostics.eventlog.9.0.6.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Memory.Data/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qK9dBegf3wdw2+mqeIXmTJjlMq4sn39wUN+JSagMuCmp/05ZyQLYIZsxlCopChxcgRwqu/+tHXKRaKtzHYlITw==", "path": "system.memory.data/9.0.5", "hashPath": "system.memory.data.9.0.5.nupkg.sha512"}, "System.Net.ServerSentEvents/10.0.0-preview.4.25258.110": {"type": "package", "serviceable": true, "sha512": "sha512-5SG8yFN0e6y3VVaCMWSRYdDW15Apzg2LQoYUtl+jFrGb/+RLSMYmIdU9hO1hbHqpU0JD/lfBlCox0FebdAGGOQ==", "path": "system.net.serversentevents/10.0.0-preview.4.25258.110", "hashPath": "system.net.serversentevents.10.0.0-preview.4.25258.110.nupkg.sha512"}, "System.Numerics.Tensors/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hyJB4UlpAi19Xr9AXzu2NuagKC4lPfHObNMEAA0HmqFz2rX7wKgzeYzO/jM/eBHDhnUGFFEjk5cOoJaxqg5J4A==", "path": "system.numerics.tensors/9.0.0", "hashPath": "system.numerics.tensors.9.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}, "System.Threading.Channels/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-mxPWL9cgQos5PipA/1kg+GdG90HmMycQ077S5mlIWu+V1CGgVbmFiWcKRL+EQPSR8pmW1yVfzGEOrv0IJnrphA==", "path": "system.threading.channels/9.0.5", "hashPath": "system.threading.channels.9.0.5.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Yidev.Agent.Oidc/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UGH1QttJEfeeI6x/WzFGPVMv8vPD+QlPGCsuCYS8XIPITO3+s0aU9haLfiwAjgerhz4scOA0FcnvVW3w4De/kw==", "path": "yidev.agent.oidc/1.0.1", "hashPath": "yidev.agent.oidc.1.0.1.nupkg.sha512"}, "Microsoft.Web.WebView2.Core/1.0.3296.44": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.WinForms/1.0.3296.44": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Wpf/1.0.3296.44": {"type": "reference", "serviceable": false, "sha512": ""}}}