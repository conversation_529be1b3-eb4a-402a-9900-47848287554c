2025-07-07 10:10:28.341 +08:00 [INF] Application Shutting Down
2025-07-07 10:10:28.355 +08:00 [DBG] Hosting stopping
2025-07-07 10:10:28.356 +08:00 [INF] Application is shutting down...
2025-07-07 10:10:28.358 +08:00 [DBG] Hosting stopped
2025-07-07 10:17:33.330 +08:00 [DBG] Hosting starting
2025-07-07 10:17:33.414 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:17:33.420 +08:00 [INF] Hosting environment: Production
2025-07-07 10:17:33.423 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:17:33.425 +08:00 [DBG] Hosting started
2025-07-07 10:17:33.426 +08:00 [INF] Application Starting Up
2025-07-07 10:17:36.674 +08:00 [DBG] warn: 2025/7/7 10:17:36.674 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:17:36.848 +08:00 [DBG] info: 2025/7/7 10:17:36.848 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:17:36.854 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:17:37.524 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:17:37.872 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:17:41.626 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:17:41.632 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:17:42.134 +08:00 [DBG] info: 2025/7/7 10:17:42.134 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:17:42.193 +08:00 [INF] Getting messages for topic ID: 9
2025-07-07 10:17:42.207 +08:00 [DBG] info: 2025/7/7 10:17:42.207 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:17:42.230 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-07 10:17:48.151 +08:00 [INF] Creating topic '新话题 10:17:48' for user: llk
2025-07-07 10:17:48.256 +08:00 [DBG] info: 2025/7/7 10:17:48.256 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T10:17:48.1538272+08:00' (DbType = DateTime), @p1='新话题 10:17:48' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 10:17:48.300 +08:00 [INF] Topic '新话题 10:17:48' created with ID: 10
2025-07-07 10:17:48.306 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:17:48.310 +08:00 [DBG] info: 2025/7/7 10:17:48.310 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:17:48.312 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 10:17:53.159 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:17:53.168 +08:00 [DBG] info: 2025/7/7 10:17:53.168 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:17:53.1587435+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:17:53.178 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 10:17:54.217 +08:00 [ERR] Error generating topic title
System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.JsonDocument.Parse(ReadOnlySpan`1 utf8JsonSpan, JsonReaderOptions readerOptions, MetadataDb& database, StackRowStack& stack)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonReaderOptions readerOptions, Byte[] extraRentedArrayPoolBytes, PooledByteBufferWriter extraPooledByteBufferWriter)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonDocumentOptions options)
   at OpenAI.Chat.ChatCompletion.op_Explicit(ClientResult result)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.ChatCompletion.ChatCompletionServiceExtensions.GetChatMessageContentAsync(IChatCompletionService chatCompletionService, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.GenerateTopicTitleAsync(String userMessage) in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 146
2025-07-07 10:17:54.258 +08:00 [INF] Updating topic ID: 10
2025-07-07 10:17:54.264 +08:00 [DBG] info: 2025/7/7 10:17:54.263 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='10', @p0='2025-07-07T10:17:48.1538272+08:00' (DbType = DateTime), @p1='你好' (Nullable = false) (Size = 2), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 10:17:54.273 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:19:26.052 +08:00 [DBG] Hosting starting
2025-07-07 10:19:26.113 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:19:26.120 +08:00 [INF] Hosting environment: Production
2025-07-07 10:19:26.122 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:19:26.124 +08:00 [DBG] Hosting started
2025-07-07 10:19:26.126 +08:00 [INF] Application Starting Up
2025-07-07 10:19:27.083 +08:00 [DBG] warn: 2025/7/7 10:19:27.082 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:19:27.261 +08:00 [DBG] info: 2025/7/7 10:19:27.261 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (26ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:19:27.266 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:19:27.433 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:19:27.453 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:19:31.131 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:19:31.136 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:19:31.634 +08:00 [DBG] info: 2025/7/7 10:19:31.634 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:19:31.694 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:19:31.709 +08:00 [DBG] info: 2025/7/7 10:19:31.709 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:19:31.732 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-07 10:19:38.006 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:19:38.116 +08:00 [DBG] info: 2025/7/7 10:19:38.115 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:19:38.0056740+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:19:38.160 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:20:06.233 +08:00 [DBG] Hosting starting
2025-07-07 10:20:06.293 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:20:06.299 +08:00 [INF] Hosting environment: Production
2025-07-07 10:20:06.302 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:20:06.304 +08:00 [DBG] Hosting started
2025-07-07 10:20:06.305 +08:00 [INF] Application Starting Up
2025-07-07 10:20:07.255 +08:00 [DBG] warn: 2025/7/7 10:20:07.255 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:20:07.436 +08:00 [DBG] info: 2025/7/7 10:20:07.436 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (22ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:20:07.442 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:20:07.616 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:20:07.635 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:20:10.494 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:20:10.499 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:20:11.004 +08:00 [DBG] info: 2025/7/7 10:20:11.004 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:20:11.065 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:20:11.080 +08:00 [DBG] info: 2025/7/7 10:20:11.080 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:20:11.102 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 10:20:16.265 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:20:16.373 +08:00 [DBG] info: 2025/7/7 10:20:16.373 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:20:16.2647830+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:20:16.400 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:44:13.804 +08:00 [DBG] Hosting starting
2025-07-07 10:44:13.891 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:44:13.897 +08:00 [INF] Hosting environment: Production
2025-07-07 10:44:13.900 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:44:13.901 +08:00 [DBG] Hosting started
2025-07-07 10:44:13.903 +08:00 [INF] Application Starting Up
2025-07-07 10:44:17.166 +08:00 [DBG] warn: 2025/7/7 10:44:17.166 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:44:17.352 +08:00 [DBG] info: 2025/7/7 10:44:17.352 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (30ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:44:17.358 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:44:18.005 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:44:18.348 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:44:21.837 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:44:21.843 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:44:22.344 +08:00 [DBG] info: 2025/7/7 10:44:22.344 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:44:22.404 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:44:22.420 +08:00 [DBG] info: 2025/7/7 10:44:22.420 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:44:22.442 +08:00 [INF] Setting conversation history. Message count: 3
2025-07-07 10:44:29.344 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:44:29.457 +08:00 [DBG] info: 2025/7/7 10:44:29.457 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:44:29.3441439+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:44:29.484 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:44:47.212 +08:00 [DBG] Hosting starting
2025-07-07 10:44:47.275 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:44:47.281 +08:00 [INF] Hosting environment: Production
2025-07-07 10:44:47.283 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:44:47.285 +08:00 [DBG] Hosting started
2025-07-07 10:44:47.287 +08:00 [INF] Application Starting Up
2025-07-07 10:44:48.268 +08:00 [DBG] warn: 2025/7/7 10:44:48.267 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:44:48.445 +08:00 [DBG] info: 2025/7/7 10:44:48.445 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (25ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:44:48.451 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:44:48.617 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:44:48.635 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:44:51.363 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:44:51.368 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:44:51.870 +08:00 [DBG] info: 2025/7/7 10:44:51.870 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:44:51.929 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:44:51.945 +08:00 [DBG] info: 2025/7/7 10:44:51.945 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:44:51.968 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-07 10:44:56.460 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:44:56.578 +08:00 [DBG] info: 2025/7/7 10:44:56.578 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:44:56.4592618+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:44:56.724 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:50:19.578 +08:00 [DBG] Hosting starting
2025-07-07 10:50:19.637 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:50:19.644 +08:00 [INF] Hosting environment: Production
2025-07-07 10:50:19.646 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:50:19.647 +08:00 [DBG] Hosting started
2025-07-07 10:50:19.648 +08:00 [INF] Application Starting Up
2025-07-07 10:50:20.596 +08:00 [DBG] warn: 2025/7/7 10:50:20.595 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:50:20.769 +08:00 [DBG] info: 2025/7/7 10:50:20.769 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (22ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:50:20.775 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:50:20.944 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:50:20.963 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:50:24.010 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:50:24.016 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:50:24.522 +08:00 [DBG] info: 2025/7/7 10:50:24.522 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:50:24.581 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:50:24.596 +08:00 [DBG] info: 2025/7/7 10:50:24.596 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:50:24.619 +08:00 [INF] Setting conversation history. Message count: 5
2025-07-07 10:50:26.881 +08:00 [INF] Deleting topic ID: 10
2025-07-07 10:50:26.891 +08:00 [DBG] info: 2025/7/7 10:50:26.891 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topic_Id_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topic_Id_0
2025-07-07 10:50:26.997 +08:00 [DBG] info: 2025/7/7 10:50:26.997 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='24'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.003 +08:00 [DBG] info: 2025/7/7 10:50:27.003 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='25'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.005 +08:00 [DBG] info: 2025/7/7 10:50:27.005 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='26'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.009 +08:00 [DBG] info: 2025/7/7 10:50:27.009 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='27'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.011 +08:00 [DBG] info: 2025/7/7 10:50:27.011 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='28'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.014 +08:00 [DBG] info: 2025/7/7 10:50:27.014 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='10'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "Topics"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.048 +08:00 [INF] Topic ID: 10 deleted.
2025-07-07 10:50:29.353 +08:00 [INF] Creating topic '新话题 10:50:29' for user: llk
2025-07-07 10:50:29.379 +08:00 [DBG] info: 2025/7/7 10:50:29.379 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T10:50:29.3552224+08:00' (DbType = DateTime), @p1='新话题 10:50:29' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 10:50:29.395 +08:00 [INF] Topic '新话题 10:50:29' created with ID: 11
2025-07-07 10:50:29.397 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 10:50:29.402 +08:00 [DBG] info: 2025/7/7 10:50:29.402 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:50:29.404 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 10:50:34.604 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 10:50:34.612 +08:00 [DBG] info: 2025/7/7 10:50:34.612 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:50:34.6039057+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:50:34.622 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 10:50:35.653 +08:00 [ERR] Error generating topic title
System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.JsonDocument.Parse(ReadOnlySpan`1 utf8JsonSpan, JsonReaderOptions readerOptions, MetadataDb& database, StackRowStack& stack)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonReaderOptions readerOptions, Byte[] extraRentedArrayPoolBytes, PooledByteBufferWriter extraPooledByteBufferWriter)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonDocumentOptions options)
   at OpenAI.Chat.ChatCompletion.op_Explicit(ClientResult result)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.ChatCompletion.ChatCompletionServiceExtensions.GetChatMessageContentAsync(IChatCompletionService chatCompletionService, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.GenerateTopicTitleAsync(String userMessage) in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 146
2025-07-07 10:50:35.693 +08:00 [INF] Updating topic ID: 11
2025-07-07 10:50:35.697 +08:00 [DBG] info: 2025/7/7 10:50:35.697 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='11', @p0='2025-07-07T10:50:29.3552224+08:00' (DbType = DateTime), @p1='你好' (Nullable = false) (Size = 2), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 10:50:35.701 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:51:21.661 +08:00 [DBG] Hosting starting
2025-07-07 10:51:21.726 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:51:21.733 +08:00 [INF] Hosting environment: Production
2025-07-07 10:51:21.735 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:51:21.737 +08:00 [DBG] Hosting started
2025-07-07 10:51:21.739 +08:00 [INF] Application Starting Up
2025-07-07 10:51:22.685 +08:00 [DBG] warn: 2025/7/7 10:51:22.685 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:51:22.860 +08:00 [DBG] info: 2025/7/7 10:51:22.860 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (24ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:51:22.865 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:51:23.029 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:51:23.048 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:51:27.401 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:51:27.406 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:51:27.926 +08:00 [DBG] info: 2025/7/7 10:51:27.926 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:51:27.986 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 10:51:28.001 +08:00 [DBG] info: 2025/7/7 10:51:28.001 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:51:28.025 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-07 10:55:51.193 +08:00 [INF] Application Shutting Down
2025-07-07 10:55:51.197 +08:00 [DBG] Hosting stopping
2025-07-07 10:55:51.200 +08:00 [INF] Application is shutting down...
2025-07-07 10:55:51.206 +08:00 [DBG] Hosting stopped
