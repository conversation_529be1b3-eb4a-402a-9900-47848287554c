﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using ModelContextProtocol.SemanticKernel;
using System.Threading.Tasks;
using Yidev.LocalAI.Models;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using ModelContextProtocol.SemanticKernel.Extensions;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using System.IO;
using System.Text;
using Serilog;

namespace Yidev.LocalAI.Services
{
    public class SemanticKernelService
    {
        private readonly Kernel _kernel;
        private readonly IChatCompletionService _chatCompletionService;
        private readonly ChatHistory _chatHistory;

        private SemanticKernelService()
        {
            Log.Information("Initializing SemanticKernelService...");
            var builder = Kernel.CreateBuilder();
            //builder.Services.AddLogging(c => c.AddDebug().SetMinimumLevel(LogLevel.Trace));
            // 使用本地的与OpenAI兼容的终结点进行配置
            builder.AddOpenAIChatCompletion(
                modelId: "gemini-2.5-pro-preview-06-05",
                apiKey: "",
                endpoint: new System.Uri("https://openai.ai.yidev.cn/v1")); // 本地AI服务器的标准终结点
            _kernel = builder.Build();
            
            _chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();
            _chatHistory = new ChatHistory();
            Log.Information("SemanticKernelService initialized.");
        }

        private async Task InitializeAsync()
        {
            try
            {
                var dir = Path.Combine(AppContext.BaseDirectory, "uploads");
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                var fsPlugin = await _kernel.Plugins.AddMcpFunctionsFromStdioServerAsync("filesystem", "npx", ["-y", "@modelcontextprotocol/server-filesystem", dir]);
                Log.Information($"MCP Filesystem 插件已添加: {fsPlugin.Name}");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "在 Kernel 工厂方法中添加共享 MCP 插件时出错");
                // 抛出异常会阻止 Kernel 创建，这通常是期望的行为
                throw new InvalidOperationException("无法初始化共享 MCP 插件。", ex);
            }
        }

        public static async Task<SemanticKernelService> CreateAsync()
        {
            var service = new SemanticKernelService();
            await service.InitializeAsync();
            return service;
        }

        public void SetConversationHistory(IEnumerable<ChatMessage> messages)
        {
            Log.Information("Setting conversation history. Message count: {MessageCount}", messages.Count());
            _chatHistory.Clear();
            _chatHistory.AddSystemMessage("你是一个集成在C# WPF应用中的AI助手。请保持回答简洁有用。");

            foreach (var message in messages)
            {
                if (message.Role == "user")
                {
                    _chatHistory.AddUserMessage(message.Content);
                }
                else if (message.Role == "AI助手" || message.Role == "assistant")
                {
                    _chatHistory.AddAssistantMessage(message.Content);
                }
            }
        }

        public async Task<string> GetChatResponseAsync(string userMessage)
        {
            Log.Information("Getting chat response for user message: {UserMessage}", userMessage);
            _chatHistory.AddUserMessage(userMessage);

            var executionSettings = new OpenAIPromptExecutionSettings
            {
                //MaxTokens = 1000000,
                Temperature = 0,
                // 使用 ToolCallBehavior.AutoInvokeKernelFunctions 来让 Semantic Kernel 自动处理函数/工具的调用和执行。
                // 这通常是推荐的方式，前提是 Kernel 实例 (_kernel) 被传递给聊天完成服务。
                ToolCallBehavior = ToolCallBehavior.AutoInvokeKernelFunctions,
                // FunctionChoiceBehavior = FunctionChoiceBehavior.Auto() // 旧版或特定场景下的设置，ToolCallBehavior 优先
            };

            /*
            // 使用 GetStreamingChatMessageContentsAsync 获取更丰富的流式内容，包括函数调用
            IAsyncEnumerable<StreamingChatMessageContent> streamingResults = _chatCompletionService.GetStreamingChatMessageContentsAsync(
                _chatHistory,
                executionSettings,
                _kernel); // 传递 Kernel 以便执行 Function Call
            */
            var result = await _chatCompletionService.GetChatMessageContentAsync(
                _chatHistory,
                executionSettings,
                _kernel); // 传递 Kernel 以便执行 Function Call
            _chatHistory.Add(result); // 将助手的响应添加到历史记录中，以备下一轮使用

            Log.Information("Received chat response: {ResponseContent}", result.Content);
            return result.Content;
        }

        /// <summary>
        /// 根据用户消息生成简洁的话题标题
        /// </summary>
        /// <param name="userMessage">用户的第一条消息</param>
        /// <returns>生成的话题标题</returns>
        public async Task<string> GenerateTopicTitleAsync(string userMessage)
        {
            Log.Information("Generating topic title for user message: {UserMessage}", userMessage);

            var tempChatHistory = new ChatHistory();
            tempChatHistory.AddSystemMessage("你是一个专门生成对话标题的助手。请根据用户的问题或消息，生成一个简洁、准确的标题，不超过15个字符。只返回标题内容，不要包含任何其他文字或标点符号。");
            tempChatHistory.AddUserMessage($"请为以下消息生成一个简洁的标题：{userMessage}");

            var executionSettings = new OpenAIPromptExecutionSettings
            {
                MaxTokens = 50,
                Temperature = 0.3,
            };

            try
            {
                var result = await _chatCompletionService.GetChatMessageContentAsync(
                    tempChatHistory,
                    executionSettings,
                    _kernel);

                var title = result.Content?.Trim();

                // 如果生成的标题为空或过长，使用默认格式
                if (string.IsNullOrWhiteSpace(title) || title.Length > 20)
                {
                    title = userMessage.Length > 15 ? userMessage.Substring(0, 15) + "..." : userMessage;
                }

                Log.Information("Generated topic title: {Title}", title);
                return title;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error generating topic title");
                // 如果生成失败，使用用户消息的前15个字符作为标题
                var fallbackTitle = userMessage.Length > 15 ? userMessage.Substring(0, 15) + "..." : userMessage;
                return fallbackTitle;
            }
        }
    }
}
